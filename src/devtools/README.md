# Debug Data Visualization System

A comprehensive debugging visualization system for analyzing data arrays in C++ applications. The system consists of a header-only C++ library for data export and a Python script for interactive visualization.

## Overview

This system enables developers to easily export debug data from C++ code and visualize it using Python plots. Data is automatically organized by measurement sessions and grouped for comparison analysis.

**Key Features:**
- Header-only C++ implementation for easy integration
- Template-based design supporting multiple data types
- Automatic file organization and type detection
- Interactive Python visualization with matplotlib
- Real-time monitoring for new data files
- Sequential measurement display with user control

## Components

### 1. C++ Data Exporter (`data_exporter.hpp`)

Header-only template library that exports data arrays to binary files.

**Supported Data Types:**
- `float`, `double`
- `int8_t`, `uint8_t`, `int16_t`, `uint16_t`
- `int32_t`, `uint32_t`, `int64_t`, `uint64_t`

**File Naming Convention:**
```
dev_debug/{prefixName}_{measurementId}_{dataName}_{dataType}.bin
```

### 2. Python Visualizer (`visualizer.py`)

Interactive visualization script that scans for binary files and creates plots grouped by measurement sessions.

## C++ Usage

### Basic Integration

1. Include the header in your C++ code:
```cpp
#include "devtools/data_exporter.hpp"
```

2. Export debug data using the template function:
```cpp
template<typename T>
bool export_debug_data(const std::string& prefixName, 
                      const std::string& dataName,
                      int measurementId, 
                      const T* data, 
                      size_t elements);
```

### Usage Examples

**Example 1: Signal Processing Debug**
```cpp
#include "devtools/data_exporter.hpp"

void processSignal() {
    // Generate test signal
    std::vector<float> input_signal(1000);
    std::vector<float> filtered_signal(1000);
    
    // ... signal processing code ...
    
    // Export debug data for measurement session 42
    DevTools::export_debug_data("filter", "input", 42, 
                                input_signal.data(), input_signal.size());
    DevTools::export_debug_data("filter", "output", 42, 
                                filtered_signal.data(), filtered_signal.size());
}
```

**Example 2: Multiple Data Types**
```cpp
void debugDemodulator() {
    std::vector<uint32_t> raw_samples(2048);
    std::vector<int16_t> i_channel(1024);
    std::vector<int16_t> q_channel(1024);
    std::vector<float> magnitude(1024);
    
    // ... demodulation processing ...
    
    int session_id = 100;
    DevTools::export_debug_data("demod", "raw", session_id, 
                                raw_samples.data(), raw_samples.size());
    DevTools::export_debug_data("demod", "i_channel", session_id, 
                                i_channel.data(), i_channel.size());
    DevTools::export_debug_data("demod", "q_channel", session_id, 
                                q_channel.data(), q_channel.size());
    DevTools::export_debug_data("demod", "magnitude", session_id, 
                                magnitude.data(), magnitude.size());
}
```

**Example 3: Error Handling**
```cpp
bool exportWithErrorHandling() {
    std::vector<double> data = {1.0, 2.0, 3.0, 4.0, 5.0};
    
    if (!DevTools::export_debug_data("test", "sample", 1, 
                                     data.data(), data.size())) {
        std::cerr << "Failed to export debug data!" << std::endl;
        return false;
    }
    
    std::cout << "Debug data exported successfully" << std::endl;
    return true;
}
```

## Python Visualization

### Installation Requirements

```bash
pip install numpy matplotlib
```

### Running the Visualizer

**One-time scan and display:**
```bash
cd src/devtools
python visualizer.py
```

**Continuous monitoring mode:**
```bash
python visualizer.py --watch
```

**Custom debug directory:**
```bash
python visualizer.py --debug-dir /path/to/debug/files
```

### Visualization Features

- **Automatic Grouping**: Files are grouped by `(prefixName, measurementId)`
- **Sequential Display**: Measurements are shown one at a time, sorted by prefix and ID
- **Interactive Control**: Close current plot window to proceed to next measurement
- **Multi-dataset Plots**: All datasets from the same measurement are plotted together
- **Type-aware Reading**: Binary data is read according to the C++ data type
- **Legend Support**: Each dataset is labeled with name and data type

## Complete Workflow Example

### 1. C++ Code Export
```cpp
// In your signal processing function
void analyzeSignal() {
    std::vector<float> raw_data(1024);
    std::vector<float> filtered_data(1024);
    std::vector<float> fft_magnitude(512);
    
    // ... processing steps ...
    
    // Export all data for measurement session 1
    DevTools::export_debug_data("analysis", "raw", 1, 
                                raw_data.data(), raw_data.size());
    DevTools::export_debug_data("analysis", "filtered", 1, 
                                filtered_data.data(), filtered_data.size());
    DevTools::export_debug_data("analysis", "fft_mag", 1, 
                                fft_magnitude.data(), fft_magnitude.size());
}
```

### 2. Run Your C++ Application
```bash
# Compile and run your application
make build
./your_application
```

### 3. Visualize Results
```bash
# Navigate to devtools directory
cd src/devtools

# Run visualizer
python visualizer.py
```

### 4. Expected Output
```
Scanning dev_debug for debug files...
Found 1 measurement(s)

Found measurement: analysis_1
  Datasets: ['raw', 'filtered', 'fft_mag']
  Plotted raw: 1024 samples (float)
  Plotted filtered: 1024 samples (float)
  Plotted fft_mag: 512 samples (float)
Displaying plot for analysis_1. Close window to continue...
```

## File Organization

```
src/devtools/
├── data_exporter.hpp    # C++ header-only library
├── visualizer.py        # Python visualization script
├── README.md           # This documentation
└── .gitignore          # Excludes debug artifacts

dev_debug/              # Created automatically
├── analysis_1_raw_float.bin
├── analysis_1_filtered_float.bin
└── analysis_1_fft_mag_float.bin
```

## Integration Notes

- **Header-only Design**: No compilation overhead, just include the header
- **Automatic Directory Creation**: `dev_debug/` is created automatically
- **Error Handling**: All functions return boolean success/failure status
- **Thread Safety**: Each export operation is atomic (single file write)
- **Performance**: Minimal overhead, direct binary file writing

## Troubleshooting

**Common Issues:**

1. **Permission Errors**: Ensure write permissions for `dev_debug/` directory
2. **Python Dependencies**: Install required packages: `pip install numpy matplotlib`
3. **File Not Found**: Check that C++ code successfully exported files
4. **Type Mismatches**: Verify data type names match between C++ and Python
5. **Empty Plots**: Check file sizes and data validity

**Debug Tips:**

- Use `--debug-dir` to specify custom debug directory
- Check console output for export confirmation messages
- Verify file existence in `dev_debug/` directory before running visualizer
- Use `--watch` mode for real-time debugging during development
